name: "publish"

on:
  push:
    branches:
      - main

jobs:
  publish-tauri:
    permissions:
      contents: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: "macos-latest" # for Arm based macs (M1 and above).
            args: "--target aarch64-apple-darwin"
          - platform: "macos-latest" # for Intel based macs.
            args: "--target x86_64-apple-darwin"
          - platform: "ubuntu-22.04" # for Tauri v1 you could replace this with ubuntu-20.04.
            args: ""
          - platform: "windows-latest"
            args: ""

    runs-on: ${{ matrix.platform }}
    steps:
      - uses: actions/checkout@v4

      - name: setup node
        uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - uses: oven-sh/setup-bun@v2

      - name: install Rust stable
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.platform == 'macos-latest' && 'aarch64-apple-darwin,x86_64-apple-darwin' || '' }}

      - name: install dependencies (ubuntu only)
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.0-dev libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf
        # webkitgtk 4.0 is for Tauri v1 - webkitgtk 4.1 is for Tauri v2.
        # You can remove the one that doesn't apply to your app to speed up the workflow a bit.

      - name: install frontend dependencies
        run: bun install

      - uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tagName: v__VERSION__ # 简化标签名称
          releaseName: "Release v__VERSION__"
          releaseBody: |
            launch-rs v__VERSION__ 发布说明
            
            ### 支持平台
            - Windows
            - macOS (Intel/Apple Silicon)
            - Linux
            
            ### 下载
            请在下方 Assets 中选择对应平台的安装包：
            - Windows: `.msi`
            - macOS: `.dmg`
            - Linux: `.AppImage` 或 `.deb`
          releaseDraft: false # 自动发布，不再是草稿状态
          prerelease: false
          args: ${{ matrix.args }}