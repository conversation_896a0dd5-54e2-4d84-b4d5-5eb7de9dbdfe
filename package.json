{"name": "launch-rs", "private": true, "version": "0.0.1-snapshot", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@vueuse/core": "^13.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.503.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "reka-ui": "^2.2.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "vue": "^3.5.13", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^22.15.3", "@types/vue-router": "^2.0.0", "@vitejs/plugin-vue": "^5.2.1", "typescript": "~5.8.3", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}