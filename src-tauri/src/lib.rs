// Learn more about <PERSON><PERSON> commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

/// 切换窗口的无头模式
///
/// # 参数
/// * `app` - Tauri应用实例
/// * `window_label` - 窗口标签名称，默认为"main"
/// * `headless` - 是否启用无头模式
#[tauri::command]
pub fn toggle_headless_mode<R: tauri::Runtime>(
    app: &tauri::App<R>,
    window_label: &str,
    headless: bool,
) -> () {
    // 获取指定标签的窗口
    let window = app
        .get_window(window_label)
        .ok_or_else(|| tauri::Error::WindowNotFound)?;

    if headless {
        // 启用无头模式：隐藏窗口并移除装饰
        window.hide()?;
        window.set_decorations(false)?;
    } else {
        // 禁用无头模式：显示窗口并添加装饰
        window.set_decorations(true)?;
        window.show()?;
    }

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .invoke_handler(tauri::generate_handler![greet, toggle_headless_mode])
        .setup(|app| {
            // 从环境变量获取是否启用无头模式
            let headless_mode = std::env::var("HEADLESS").unwrap_or_default() == "true";

            // 应用无头模式设置
            if let Err(e) = toggle_headless_mode(app, "main", headless_mode) {
                eprintln!("无法设置无头模式: {}", e);
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
