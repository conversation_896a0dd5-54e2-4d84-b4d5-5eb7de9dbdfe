{"$schema": "https://schema.tauri.app/config/2", "productName": "launch-rs", "version": "0.1.0", "identifier": "io.github.yeheng.launch-rs.app", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "launch-rs", "width": 800, "height": 600, "visible": true, "decorations": true, "transparent": false}], "withGlobalTauri": true, "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}